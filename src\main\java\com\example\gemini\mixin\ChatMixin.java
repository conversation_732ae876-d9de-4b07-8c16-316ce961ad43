package com.example.gemini.mixin;

import com.example.gemini.Gemini;
import net.minecraft.client.gui.screen.ChatScreen;
import net.minecraft.client.MinecraftClient;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

@Mixin(ChatScreen.class)
public class ChatMixin {
    @Inject(method = "sendMessage", at = @At("HEAD"))
    private void onSendMessage(String chatText, boolean addToHistory, CallbackInfoReturnable<Boolean> cir) {
        Gemini.LOGGER.info("Chat message intercepted: " + chatText);

        if (chatText.startsWith("!а ")) {
            String question = chatText.substring(3);
            MinecraftClient client = MinecraftClient.getInstance();

            Gemini.LOGGER.info("Gemini request detected: " + question);

            if (client.player != null) {
                Gemini.handleGeminiRequest(question, client.player);
            }
        }
    }
}
