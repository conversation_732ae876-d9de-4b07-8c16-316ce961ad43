package com.example.gemini.mixin;

import com.example.gemini.Gemini;
import net.minecraft.client.network.ClientPlayNetworkHandler;
import net.minecraft.client.MinecraftClient;
import net.minecraft.network.packet.s2c.play.GameMessageS2CPacket;
import net.minecraft.text.Text;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(ClientPlayNetworkHandler.class)
public class ChatMixin {
    @Inject(method = "sendChatMessage", at = @At("HEAD"), cancellable = true)
    private void onSendChatMessage(String message, CallbackInfo ci) {
        // Якщо це відповідь від Gemini, пропускаємо обробку
        if (Gemini.isGeminiResponse()) {
            return;
        }

        System.out.println("Outgoing chat message: " + message);
        Gemini.LOGGER.info("Outgoing chat message: " + message);

        // Не скасовуємо власні повідомлення з !а - дозволяємо їх відправити
    }

    @Inject(method = "onChatMessage", at = @At("HEAD"))
    private void onIncomingChatMessage(net.minecraft.network.packet.s2c.play.ChatMessageS2CPacket packet, CallbackInfo ci) {
        try {
            String message = packet.body().content();

            System.out.println("Incoming chat message (onChatMessage): " + message);
            Gemini.LOGGER.info("Incoming chat message (onChatMessage): " + message);

            // Перевіряємо чи повідомлення починається з !а
            if (message.startsWith("!а ")) {
                String question = message.substring(3);

                System.out.println("Gemini request detected: " + question);
                Gemini.LOGGER.info("Gemini request detected: " + question);

                MinecraftClient client = MinecraftClient.getInstance();
                if (client.player != null) {
                    Gemini.handleGeminiRequest(question, client.player);
                }
            }
        } catch (Exception e) {
            System.out.println("Error processing chat message: " + e.getMessage());
            Gemini.LOGGER.error("Error processing chat message", e);
        }
    }

    @Inject(method = "onGameMessage", at = @At("HEAD"))
    private void onGameMessage(GameMessageS2CPacket packet, CallbackInfo ci) {
        try {
            Text messageText = packet.content();
            String message = messageText.getString();

            System.out.println("Game message: " + message);
            Gemini.LOGGER.info("Game message: " + message);

            // Перевіряємо формат <PlayerName> !а question
            if (message.matches("^<[^>]+> !а .+")) {
                // Витягуємо ім'я гравця та питання
                int nameStart = message.indexOf('<') + 1;
                int nameEnd = message.indexOf('>');
                String playerName = message.substring(nameStart, nameEnd);

                int questionStart = message.indexOf("!а ") + 3;
                String question = message.substring(questionStart);

                System.out.println("Gemini request from " + playerName + ": " + question);
                Gemini.LOGGER.info("Gemini request from " + playerName + ": " + question);

                MinecraftClient client = MinecraftClient.getInstance();
                if (client.player != null) {
                    Gemini.handleGeminiRequest(question, client.player);
                }
            }
        } catch (Exception e) {
            System.out.println("Error processing game message: " + e.getMessage());
            Gemini.LOGGER.error("Error processing game message", e);
        }
    }
}
