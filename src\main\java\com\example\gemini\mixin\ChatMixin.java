package com.example.gemini.mixin;

import com.example.gemini.Gemini;
import net.minecraft.client.network.ClientPlayNetworkHandler;
import net.minecraft.client.MinecraftClient;
import net.minecraft.network.packet.s2c.play.GameMessageS2CPacket;
import net.minecraft.text.Text;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(ClientPlayNetworkHandler.class)
public class ChatMixin {
    @Inject(method = "sendChatMessage", at = @At("HEAD"), cancellable = true)
    private void onSendChatMessage(String message, CallbackInfo ci) {
        // Якщо це відповідь від Gemini, пропускаємо обробку
        if (Gemini.isGeminiResponse()) {
            return;
        }

        System.out.println("Outgoing chat message: " + message);
        Gemini.LOGGER.info("Outgoing chat message: " + message);

        // Не скасовуємо власні повідомлення з !а - дозволяємо їх відправити
    }

    @Inject(method = "onGameMessage", at = @At("HEAD"))
    private void onIncomingChatMessage(GameMessageS2CPacket packet, CallbackInfo ci) {
        Text messageText = packet.content();
        String message = messageText.getString();

        System.out.println("Incoming chat message: " + message);
        Gemini.LOGGER.info("Incoming chat message: " + message);

        // Перевіряємо формат <PlayerName> !а question
        if (message.matches("^<[^>]+> !а .+")) {
            // Витягуємо ім'я гравця та питання
            int nameStart = message.indexOf('<') + 1;
            int nameEnd = message.indexOf('>');
            String playerName = message.substring(nameStart, nameEnd);

            int questionStart = message.indexOf("!а ") + 3;
            String question = message.substring(questionStart);

            System.out.println("Gemini request from " + playerName + ": " + question);
            Gemini.LOGGER.info("Gemini request from " + playerName + ": " + question);

            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player != null) {
                Gemini.handleGeminiRequest(question, client.player);
            }
        }
    }
}
