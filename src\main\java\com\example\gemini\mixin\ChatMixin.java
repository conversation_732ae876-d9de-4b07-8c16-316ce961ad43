package com.example.gemini.mixin;

import com.example.gemini.Gemini;
import net.minecraft.client.network.ClientPlayNetworkHandler;
import net.minecraft.client.MinecraftClient;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(ClientPlayNetworkHandler.class)
public class ChatMixin {
    private static boolean isGeminiResponse = false;

    @Inject(method = "sendChatMessage", at = @At("HEAD"), cancellable = true)
    private void onSendChatMessage(String message, CallbackInfo ci) {
        // Якщо це відповідь від Gemini, пропускаємо обробку
        if (isGeminiResponse) {
            isGeminiResponse = false;
            return;
        }

        System.out.println("Chat message intercepted: " + message);
        Gemini.LOGGER.info("Chat message intercepted: " + message);

        if (message.startsWith("!а ")) {
            String question = message.substring(3);
            System.out.println("Gemini request detected: " + question);
            Gemini.LOGGER.info("Gemini request detected: " + question);

            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player != null) {
                Gemini.handleGeminiRequest(question, client.player);
            }

            // Скасовуємо оригінальне повідомлення
            ci.cancel();
        }
    }

    public static void setGeminiResponse() {
        isGeminiResponse = true;
    }
}
