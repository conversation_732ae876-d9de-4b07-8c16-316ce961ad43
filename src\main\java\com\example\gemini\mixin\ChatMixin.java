package com.example.gemini.mixin;

import com.example.gemini.Gemini;
import net.minecraft.network.message.MessageType;
import net.minecraft.network.message.SignedMessage;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.server.PlayerManager;
import net.minecraft.text.Text;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(PlayerManager.class)
public class ChatMixin {
    @Inject(method = "broadcast(Lnet/minecraft/network/message/SignedMessage;Lnet/minecraft/server/network/ServerPlayerEntity;Lnet/minecraft/network/message/MessageType$Parameters;)V", 
            at = @At("HEAD"))
    private void onChatMessage(SignedMessage message, ServerPlayerEntity sender, MessageType.Parameters params, CallbackInfo ci) {
        String messageText = message.getContent().getString();
        String playerName = sender.getName().getString();
        
        // Перевіряємо чи повідомлення починається з "!а"
        if (messageText.startsWith("!а ")) {
            String question = messageText.substring(3); // Видаляємо "!а "
            Gemini.handleGeminiRequest(question, sender);
        }
    }
}
