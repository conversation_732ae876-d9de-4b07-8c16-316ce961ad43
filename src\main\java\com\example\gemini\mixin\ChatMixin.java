package com.example.gemini.mixin;

import com.example.gemini.Gemini;
import net.minecraft.client.network.ClientPlayNetworkHandler;
import net.minecraft.client.MinecraftClient;
import net.minecraft.network.packet.s2c.play.GameMessageS2CPacket;
import net.minecraft.text.Text;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(ClientPlayNetworkHandler.class)
public class ChatMixin {
    private static boolean isProcessing = false;

    @Inject(method = "sendChatMessage", at = @At("HEAD"), cancellable = true)
    private void onSendChatMessage(String message, CallbackInfo ci) {
        // Якщо це відповідь від Gemini, пропускаємо обробку
        if (Gemini.isGeminiResponse()) {
            return;
        }

        // Не логуємо кожне повідомлення
    }

    @Inject(method = "onChatMessage", at = @At("HEAD"))
    private void onIncomingChatMessage(net.minecraft.network.packet.s2c.play.ChatMessageS2CPacket packet, CallbackInfo ci) {
        // Запобігаємо дублюванню обробки
        if (isProcessing) {
            return;
        }

        try {
            String message = packet.body().content();

            // Перевіряємо чи повідомлення починається з !а
            if (message.startsWith("!а ")) {
                isProcessing = true;
                String question = message.substring(3);

                System.out.println("Gemini request: " + question);
                Gemini.LOGGER.info("Gemini request: " + question);

                MinecraftClient client = MinecraftClient.getInstance();
                if (client.player != null) {
                    Gemini.handleGeminiRequest(question, client.player);
                }

                // Скидаємо флаг через невеликий час
                new Thread(() -> {
                    try {
                        Thread.sleep(1000);
                        isProcessing = false;
                    } catch (InterruptedException e) {
                        isProcessing = false;
                    }
                }).start();
            }
        } catch (Exception e) {
            isProcessing = false;
            Gemini.LOGGER.error("Error processing chat message", e);
        }
    }

    @Inject(method = "onGameMessage", at = @At("HEAD"))
    private void onGameMessage(GameMessageS2CPacket packet, CallbackInfo ci) {
        // Запобігаємо дублюванню обробки
        if (isProcessing) {
            return;
        }

        try {
            Text messageText = packet.content();
            String message = messageText.getString();

            // Перевіряємо формат <PlayerName> !а question
            if (message.matches("^<[^>]+> !а .+")) {
                isProcessing = true;

                // Витягуємо ім'я гравця та питання
                int nameStart = message.indexOf('<') + 1;
                int nameEnd = message.indexOf('>');
                String playerName = message.substring(nameStart, nameEnd);

                int questionStart = message.indexOf("!а ") + 3;
                String question = message.substring(questionStart);

                System.out.println("Gemini request from " + playerName + ": " + question);
                Gemini.LOGGER.info("Gemini request from " + playerName + ": " + question);

                MinecraftClient client = MinecraftClient.getInstance();
                if (client.player != null) {
                    Gemini.handleGeminiRequest(question, client.player);
                }

                // Скидаємо флаг через невеликий час
                new Thread(() -> {
                    try {
                        Thread.sleep(1000);
                        isProcessing = false;
                    } catch (InterruptedException e) {
                        isProcessing = false;
                    }
                }).start();
            }
        } catch (Exception e) {
            isProcessing = false;
            Gemini.LOGGER.error("Error processing game message", e);
        }
    }
}
