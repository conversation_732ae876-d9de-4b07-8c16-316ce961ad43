package com.example.gemini.mixin;

import com.example.gemini.Gemini;
import net.minecraft.client.network.ClientPlayNetworkHandler;
import net.minecraft.client.MinecraftClient;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(ClientPlayNetworkHandler.class)
public class ChatMixin {
    @Inject(method = "sendChatMessage", at = @At("HEAD"))
    private void onSendChatMessage(String message, CallbackInfo ci) {
        Gemini.LOGGER.info("Chat message intercepted: " + message);

        if (message.startsWith("!а ")) {
            String question = message.substring(3);
            MinecraftClient client = MinecraftClient.getInstance();

            Gemini.LOGGER.info("Gemini request detected: " + question);

            if (client.player != null) {
                Gemini.handleGeminiRequest(question, client.player);
            }
        }
    }
}
