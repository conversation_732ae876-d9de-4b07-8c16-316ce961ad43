package com.example.gemini;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletableFuture;

public class GeminiClient {
    private static final String API_KEY = "AIzaSyBi11VCTntbs_C15KpbcYbP1RLjEzEDnpM";
    private static final String API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key=" + API_KEY;
    private static final int MAX_RESPONSE_LENGTH = 256;

    private final CloseableHttpClient httpClient;
    private final Gson gson;

    public GeminiClient() {
        this.httpClient = HttpClients.createDefault();
        this.gson = new Gson();
    }
    
    public CompletableFuture<String> generateResponse(String prompt) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                String enhancedPrompt = prompt + " Відповідь має бути короткою, максимум 200 символів, повністю закінченою думкою.";

                // Створюємо JSON запит
                JsonObject requestBody = new JsonObject();

                JsonArray contents = new JsonArray();
                JsonObject content = new JsonObject();
                JsonArray parts = new JsonArray();
                JsonObject part = new JsonObject();
                part.addProperty("text", enhancedPrompt);
                parts.add(part);
                content.add("parts", parts);
                contents.add(content);
                requestBody.add("contents", contents);

                JsonObject generationConfig = new JsonObject();
                generationConfig.addProperty("maxOutputTokens", 100);
                generationConfig.addProperty("temperature", 0.7);
                requestBody.add("generationConfig", generationConfig);

                // Створюємо HTTP запит
                HttpPost httpPost = new HttpPost(API_URL);
                httpPost.setHeader("Content-Type", "application/json");
                httpPost.setEntity(new StringEntity(gson.toJson(requestBody), "UTF-8"));

                // Виконуємо запит
                try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                    HttpEntity entity = response.getEntity();
                    String responseBody = EntityUtils.toString(entity);


                    JsonObject responseJson = gson.fromJson(responseBody, JsonObject.class);

                    if (responseJson.has("candidates") && responseJson.getAsJsonArray("candidates").size() > 0) {
                        JsonObject candidate = responseJson.getAsJsonArray("candidates").get(0).getAsJsonObject();
                        if (candidate.has("content")) {
                            JsonObject contentObj = candidate.getAsJsonObject("content");
                            if (contentObj.has("parts") && contentObj.getAsJsonArray("parts").size() > 0) {
                                JsonObject partObj = contentObj.getAsJsonArray("parts").get(0).getAsJsonObject();
                                if (partObj.has("text")) {
                                    String text = partObj.get("text").getAsString();
                                    return truncateResponse(text);
                                }
                            }
                        }
                    }

                    return "Вибачте, не можу відповісти на це питання.";
                }

            } catch (Exception e) {
                Gemini.LOGGER.error("Помилка при запиті до Gemini API", e);
                return "Помилка при отриманні відповіді.";
            }
        });
    }

    private String truncateResponse(String text) {
        // Видаляємо емодзі та інші неприпустимі символи
        text = text.replaceAll("[^\\p{L}\\p{N}\\p{P}\\p{Z}]", "");

        if (text.length() <= MAX_RESPONSE_LENGTH) {
            return text;
        }
        int lastSentenceEnd = text.lastIndexOf('.', MAX_RESPONSE_LENGTH - 1);
        if (lastSentenceEnd == -1) {
            lastSentenceEnd = text.lastIndexOf('!', MAX_RESPONSE_LENGTH - 1);
        }
        if (lastSentenceEnd == -1) {
            lastSentenceEnd = text.lastIndexOf('?', MAX_RESPONSE_LENGTH - 1);
        }

        if (lastSentenceEnd > 0) {
            return text.substring(0, lastSentenceEnd + 1);
        } else {
            int lastSpace = text.lastIndexOf(' ', MAX_RESPONSE_LENGTH - 4);
            if (lastSpace > 0) {
                return text.substring(0, lastSpace) + "...";
            } else {
                return text.substring(0, MAX_RESPONSE_LENGTH - 3) + "...";
            }
        }
    }
    
    public void shutdown() {
        try {
            httpClient.close();
        } catch (Exception e) {
            Gemini.LOGGER.error("Помилка при закритті HTTP клієнта", e);
        }
    }
}
