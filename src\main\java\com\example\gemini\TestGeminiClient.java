package com.example.gemini;

import java.util.concurrent.CompletableFuture;

public class TestGeminiClient {
    public static void main(String[] args) {
        GeminiClient client = new GeminiClient();
        
        System.out.println("Тестуємо Gemini API...");
        
        CompletableFuture<String> response = client.generateResponse("Привіт! Як справи?");
        
        response.thenAccept(result -> {
            System.out.println("Відповідь від Gemini: " + result);
            client.shutdown();
        }).exceptionally(throwable -> {
            System.err.println("Помилка: " + throwable.getMessage());
            client.shutdown();
            return null;
        });
        
        // Чекаємо завершення
        try {
            Thread.sleep(10000); // 10 секунд
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
