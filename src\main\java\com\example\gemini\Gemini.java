package com.example.gemini;

import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.network.ClientPlayerEntity;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import net.minecraft.text.Text;
import org.lwjgl.glfw.GLFW;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;

public class Gemini implements ClientModInitializer {
	public static final String MOD_ID = "gemini";


	public static final Logger LOGGER = LoggerFactory.getLogger(MOD_ID);

	private static GeminiClient geminiClient;
	private static KeyBinding testKeyBinding;

	@Override
	public void onInitializeClient() {
		LOGGER.info("Gemini AI Chat Bot initialized!");

		try {
			geminiClient = new GeminiClient();
			LOGGER.info("GeminiClient created successfully");
		} catch (Exception e) {
			LOGGER.error("Failed to create GeminiClient", e);
		}

		try {
			// Створюємо клавішу для тестування (G)
			testKeyBinding = KeyBindingHelper.registerKeyBinding(new KeyBinding(
				"key.gemini.test",
				InputUtil.Type.KEYSYM,
				GLFW.GLFW_KEY_G,
				"category.gemini"
			));
			LOGGER.info("Key binding registered successfully");
		} catch (Exception e) {
			LOGGER.error("Failed to register key binding", e);
		}

		try {
			// Реєструємо обробник натискання клавіші
			ClientTickEvents.END_CLIENT_TICK.register(client -> {
				try {
					while (testKeyBinding.wasPressed()) {
						LOGGER.info("Test key pressed! Testing Gemini API...");
						if (client.player != null) {
							client.player.sendMessage(Text.literal("Тестуємо Gemini API..."), false);
							handleGeminiRequest("Привіт! Як справи?", client.player);
						} else {
							LOGGER.warn("Player is null when key was pressed");
						}
					}
				} catch (Exception e) {
					LOGGER.error("Error in key press handler", e);
				}
			});
			LOGGER.info("Client tick event registered successfully");
		} catch (Exception e) {
			LOGGER.error("Failed to register client tick event", e);
		}

		// Тестуємо API одразу при ініціалізації
		LOGGER.info("Testing Gemini API immediately...");
		try {
			CompletableFuture<String> testResponse = geminiClient.generateResponse("Тест");
			testResponse.thenAccept(response -> {
				LOGGER.info("Test API response: " + response);
			}).exceptionally(throwable -> {
				LOGGER.error("Test API failed", throwable);
				return null;
			});
		} catch (Exception e) {
			LOGGER.error("Failed to test API", e);
		}
	}

	public static void handleGeminiRequest(String question, ClientPlayerEntity player) {
		LOGGER.info("Handling Gemini request: " + question);

		if (geminiClient == null) {
			LOGGER.error("Gemini client is null!");
			if (player != null) {
				player.sendMessage(Text.literal("Gemini клієнт не ініціалізований."), false);
			}
			return;
		}

		CompletableFuture<String> responseFuture = geminiClient.generateResponse(question);

		responseFuture.thenAccept(response -> {
			MinecraftClient client = MinecraftClient.getInstance();
			if (client.player != null && client.getNetworkHandler() != null) {
				// Відправляємо повідомлення як звичайне чат повідомлення
				// Це створить повідомлення у форматі <PlayerName> response
				client.getNetworkHandler().sendChatMessage(response);
			}
		}).exceptionally(throwable -> {
			LOGGER.error("Помилка при обробці запиту до Gemini", throwable);
			if (player != null) {
				player.sendMessage(Text.literal("Помилка при обробці запиту."), false);
			}
			return null;
		});
	}
}