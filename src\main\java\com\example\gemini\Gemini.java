package com.example.gemini;

import net.fabricmc.api.ModInitializer;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.network.message.MessageType;
import net.minecraft.network.message.SignedMessage;
import net.minecraft.util.Formatting;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;

public class Gemini implements ModInitializer {
	public static final String MOD_ID = "gemini";

	// This logger is used to write text to the console and the log file.
	// It is considered best practice to use your mod id as the logger's name.
	// That way, it's clear which mod wrote info, warnings, and errors.
	public static final Logger LOGGER = LoggerFactory.getLogger(MOD_ID);

	private static GeminiClient geminiClient;

	@Override
	public void onInitialize() {
		// This code runs as soon as Minecraft is in a mod-load-ready state.
		// However, some things (like resources) may still be uninitialized.
		// Proceed with mild caution.

		LOGGER.info("Gemini mod initialized!");
		geminiClient = new GeminiClient();
	}

	public static void handleGeminiRequest(String question, ServerPlayerEntity player) {
		if (geminiClient == null) {
			player.sendMessage(Text.literal("Gemini клієнт не ініціалізований."), false);
			return;
		}

		// Асинхронно отримуємо відповідь від Gemini
		CompletableFuture<String> responseFuture = geminiClient.generateResponse(question);

		responseFuture.thenAccept(response -> {
			// Відправляємо відповідь від імені гравця через чат
			String playerName = player.getName().getString();

			// Створюємо повідомлення від імені гравця
			player.getServer().execute(() -> {
				Text chatMessage = Text.literal("<" + playerName + "> " + response);

				// Відправляємо повідомлення всім гравцям
				player.getServer().getPlayerManager().broadcast(chatMessage, false);
			});
		}).exceptionally(throwable -> {
			LOGGER.error("Помилка при обробці запиту до Gemini", throwable);
			player.sendMessage(Text.literal("Помилка при обробці запиту."), false);
			return null;
		});
	}
}