package com.example.gemini;

import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.network.ClientPlayerEntity;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import net.minecraft.text.Text;
import org.lwjgl.glfw.GLFW;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;

public class Gemini implements ClientModInitializer {
	public static final String MOD_ID = "gemini";


	public static final Logger LOGGER = LoggerFactory.getLogger(MOD_ID);

	private static GeminiClient geminiClient;
	private static KeyBinding testKeyBinding;

	@Override
	public void onInitializeClient() {
		System.out.println("=== GEMINI MOD STARTING ===");
		LOGGER.info("=== GEMINI MOD STARTING ===");

		// Простий тест без HTTP
		try {
			// Створюємо клавішу для тестування (G)
			testKeyBinding = KeyBindingHelper.registerKeyBinding(new KeyBinding(
				"key.gemini.test",
				InputUtil.Type.KEYSYM,
				GLFW.GLFW_KEY_G,
				"category.gemini"
			));
			System.out.println("Key binding registered");
			LOGGER.info("Key binding registered");
		} catch (Exception e) {
			System.out.println("Failed to register key: " + e.getMessage());
			LOGGER.error("Failed to register key binding", e);
		}

		try {
			// Реєструємо обробник натискання клавіші
			ClientTickEvents.END_CLIENT_TICK.register(client -> {
				try {
					while (testKeyBinding != null && testKeyBinding.wasPressed()) {
						System.out.println("G KEY PRESSED!");
						LOGGER.info("G KEY PRESSED!");
						if (client.player != null) {
							client.player.sendMessage(Text.literal("G натиснуто! Мод працює!"), false);
							// Відправляємо просте повідомлення замість API запиту
							client.getNetworkHandler().sendChatMessage("Привіт! Це тест від Gemini мода!");
						}
					}
				} catch (Exception e) {
					System.out.println("Error in key handler: " + e.getMessage());
					LOGGER.error("Error in key press handler", e);
				}
			});
			System.out.println("Tick event registered");
			LOGGER.info("Tick event registered");
		} catch (Exception e) {
			System.out.println("Failed to register tick event: " + e.getMessage());
			LOGGER.error("Failed to register client tick event", e);
		}

		System.out.println("=== GEMINI MOD INITIALIZED ===");
		LOGGER.info("=== GEMINI MOD INITIALIZED ===");
	}

	public static void handleGeminiRequest(String question, ClientPlayerEntity player) {
		System.out.println("Simple test response for: " + question);
		LOGGER.info("Simple test response for: " + question);

		// Просто відправляємо тестове повідомлення
		MinecraftClient client = MinecraftClient.getInstance();
		if (client.player != null && client.getNetworkHandler() != null) {
			client.getNetworkHandler().sendChatMessage("Тестова відповідь на: " + question);
		}
	}
}