package com.example.gemini;

import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.network.ClientPlayerEntity;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import net.minecraft.text.Text;
import org.lwjgl.glfw.GLFW;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;

public class Gemini implements ClientModInitializer {
	public static final String MOD_ID = "gemini";


	public static final Logger LOGGER = LoggerFactory.getLogger(MOD_ID);

	private static GeminiClient geminiClient;
	private static KeyBinding testKeyBinding;

	@Override
	public void onInitializeClient() {
		System.out.println("=== GEMINI MOD STARTING ===");
		LOGGER.info("=== GEMINI MOD STARTING ===");

		// Ініціалізуємо Gemini клієнт
		try {
			geminiClient = new GeminiClient();
			System.out.println("Gemini client created");
			LOGGER.info("Gemini client created");
		} catch (Exception e) {
			System.out.println("Failed to create Gemini client: " + e.getMessage());
			LOGGER.error("Failed to create Gemini client", e);
		}

		// Створюємо клавішу для тестування (G)
		try {
			testKeyBinding = KeyBindingHelper.registerKeyBinding(new KeyBinding(
				"key.gemini.test",
				InputUtil.Type.KEYSYM,
				GLFW.GLFW_KEY_G,
				"category.gemini"
			));
			System.out.println("Key binding registered");
			LOGGER.info("Key binding registered");
		} catch (Exception e) {
			System.out.println("Failed to register key: " + e.getMessage());
			LOGGER.error("Failed to register key binding", e);
		}

		// Реєструємо обробник натискання клавіші та перехоплення чату
		try {
			ClientTickEvents.END_CLIENT_TICK.register(client -> {
				try {
					// Обробка клавіші G для тестування
					while (testKeyBinding != null && testKeyBinding.wasPressed()) {
						System.out.println("G KEY PRESSED!");
						LOGGER.info("G KEY PRESSED!");
						if (client.player != null) {
							client.player.sendMessage(Text.literal("Тестуємо Gemini API..."), false);
							handleGeminiRequest("Привіт! Як справи?", client.player);
						}
					}

					// Перехоплення чату (простий підхід)
					if (client.currentScreen instanceof net.minecraft.client.gui.screen.ChatScreen) {
						// Тут ми можемо додати логіку для перехоплення тексту
					}
				} catch (Exception e) {
					System.out.println("Error in tick handler: " + e.getMessage());
					LOGGER.error("Error in tick handler", e);
				}
			});
			System.out.println("Tick event registered");
			LOGGER.info("Tick event registered");
		} catch (Exception e) {
			System.out.println("Failed to register tick event: " + e.getMessage());
			LOGGER.error("Failed to register client tick event", e);
		}

		System.out.println("=== GEMINI MOD INITIALIZED ===");
		LOGGER.info("=== GEMINI MOD INITIALIZED ===");
	}

	public static void handleGeminiRequest(String question, ClientPlayerEntity player) {
		System.out.println("Handling Gemini request: " + question);
		LOGGER.info("Handling Gemini request: " + question);

		if (geminiClient == null) {
			System.out.println("Gemini client is null!");
			LOGGER.error("Gemini client is null!");
			MinecraftClient client = MinecraftClient.getInstance();
			if (client.player != null && client.getNetworkHandler() != null) {
				client.getNetworkHandler().sendChatMessage("Помилка: Gemini клієнт не ініціалізований");
			}
			return;
		}

		// Відправляємо запит до Gemini API
		CompletableFuture<String> responseFuture = geminiClient.generateResponse(question);

		responseFuture.thenAccept(response -> {
			System.out.println("Gemini response: " + response);
			LOGGER.info("Gemini response: " + response);

			MinecraftClient client = MinecraftClient.getInstance();
			if (client.player != null && client.getNetworkHandler() != null) {
				// Відправляємо відповідь від Gemini в чат
				client.getNetworkHandler().sendChatMessage(response);
			}
		}).exceptionally(throwable -> {
			System.out.println("Gemini API error: " + throwable.getMessage());
			LOGGER.error("Помилка при запиті до Gemini API", throwable);

			MinecraftClient client = MinecraftClient.getInstance();
			if (client.player != null && client.getNetworkHandler() != null) {
				client.getNetworkHandler().sendChatMessage("Помилка при отриманні відповіді від Gemini");
			}
			return null;
		});
	}
}