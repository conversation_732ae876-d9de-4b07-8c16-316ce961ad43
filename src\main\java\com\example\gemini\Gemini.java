package com.example.gemini;

import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.network.ClientPlayerEntity;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import net.minecraft.text.Text;
import org.lwjgl.glfw.GLFW;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;

public class Gemini implements ClientModInitializer {
	public static final String MOD_ID = "gemini";


	public static final Logger LOGGER = LoggerFactory.getLogger(MOD_ID);

	private static GeminiClient geminiClient;
	private static KeyBinding testKeyBinding;

	@Override
	public void onInitializeClient() {
		LOGGER.info("Gemini AI Chat Bot initialized!");
		geminiClient = new GeminiClient();

		// Створюємо клавішу для тестування (G)
		testKeyBinding = KeyBindingHelper.registerKeyBinding(new KeyBinding(
			"key.gemini.test",
			InputUtil.Type.KEYSYM,
			GLFW.GLFW_KEY_G,
			"category.gemini"
		));

		// Реєструємо обробник натискання клавіші
		ClientTickEvents.END_CLIENT_TICK.register(client -> {
			while (testKeyBinding.wasPressed()) {
				if (client.player != null) {
					LOGGER.info("Test key pressed! Testing Gemini API...");
					client.player.sendMessage(Text.literal("Тестуємо Gemini API..."), false);
					handleGeminiRequest("Привіт! Як справи?", client.player);
				}
			}
		});
	}

	public static void handleGeminiRequest(String question, ClientPlayerEntity player) {
		LOGGER.info("Handling Gemini request: " + question);

		if (geminiClient == null) {
			LOGGER.error("Gemini client is null!");
			if (player != null) {
				player.sendMessage(Text.literal("Gemini клієнт не ініціалізований."), false);
			}
			return;
		}

		CompletableFuture<String> responseFuture = geminiClient.generateResponse(question);

		responseFuture.thenAccept(response -> {
			MinecraftClient client = MinecraftClient.getInstance();
			if (client.player != null && client.getNetworkHandler() != null) {
				// Відправляємо повідомлення як звичайне чат повідомлення
				// Це створить повідомлення у форматі <PlayerName> response
				client.getNetworkHandler().sendChatMessage(response);
			}
		}).exceptionally(throwable -> {
			LOGGER.error("Помилка при обробці запиту до Gemini", throwable);
			if (player != null) {
				player.sendMessage(Text.literal("Помилка при обробці запиту."), false);
			}
			return null;
		});
	}
}