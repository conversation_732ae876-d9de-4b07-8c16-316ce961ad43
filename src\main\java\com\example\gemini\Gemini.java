package com.example.gemini;

import net.fabricmc.api.ClientModInitializer;
import net.minecraft.client.network.ClientPlayerEntity;
import net.minecraft.client.MinecraftClient;
import net.minecraft.text.Text;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;

public class Gemini implements ClientModInitializer {
	public static final String MOD_ID = "gemini";


	public static final Logger LOGGER = LoggerFactory.getLogger(MOD_ID);

	private static GeminiClient geminiClient;

	@Override
	public void onInitializeClient() {
		LOGGER.info("Gemini AI Chat Bot initialized!");
		geminiClient = new GeminiClient();
	}

	public static void handleGeminiRequest(String question, ClientPlayerEntity player) {
		LOGGER.info("Handling Gemini request: " + question);

		if (geminiClient == null) {
			LOGGER.error("Gemini client is null!");
			if (player != null) {
				player.sendMessage(Text.literal("Gemini клієнт не ініціалізований."), false);
			}
			return;
		}

		CompletableFuture<String> responseFuture = geminiClient.generateResponse(question);

		responseFuture.thenAccept(response -> {
			MinecraftClient client = MinecraftClient.getInstance();
			if (client.player != null && client.getNetworkHandler() != null) {
				// Відправляємо повідомлення як звичайне чат повідомлення
				client.getNetworkHandler().sendChatMessage(response);
			}
		}).exceptionally(throwable -> {
			LOGGER.error("Помилка при обробці запиту до Gemini", throwable);
			if (player != null) {
				player.sendMessage(Text.literal("Помилка при обробці запиту."), false);
			}
			return null;
		});
	}
}