# Gemini AI Chat Bot для Minecraft

Цей мод додає інтеграцію з Gemini AI в Minecraft, дозволяючи гравцям отримувати відповіді від штучного інтелекту прямо в чаті.

## Особливості

- **Клієнтська модифікація**: Мод працює повністю на стороні клієнта
- **Активація командою**: Відповіді генеруються тільки на повідомлення, що починаються з `!а`
- **Обмеження довжини**: Відповіді автоматично обмежуються до 256 символів
- **Від імені гравця**: Відповіді відображаються як повідомлення від того самого гравця

## Використання

1. Встановіть мод у папку `mods` вашого Minecraft клієнта
2. Запустіть гру з Fabric Loader
3. У чаті напишіть повідомлення, що починається з `!а`, наприклад:
   ```
   !а Який зараз день?
   !а Розкажи анекдот
   !а Як справи?
   ```
4. Мод автоматично відправить запит до Gemini AI і відповість від вашого імені

## Приклад

```
Amazo: !а який зараз день?
<Amazo> Сьогодні середа, 4 вересня 2025 року.
```

## Технічні деталі

- **Версія Minecraft**: 1.21.8
- **Loader**: Fabric
- **API**: Gemini 2.0 Flash Exp
- **Мова програмування**: Java 21

## Збірка

```bash
./gradlew build
```

Готовий JAR файл буде у папці `build/libs/`

## Конфігурація

API ключ Gemini вбудований у код. Для зміни ключа відредагуйте файл `GeminiClient.java`.

## Обмеження

- Відповіді обмежені 256 символами
- Працює тільки з повідомленнями, що починаються з `!а`
- Потребує підключення до інтернету
- Використовує вбудований API ключ (може мати ліміти)

## Ліцензія

CC0-1.0
